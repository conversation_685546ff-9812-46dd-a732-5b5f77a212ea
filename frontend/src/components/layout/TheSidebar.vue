<template>
  <div class="sidebar-container">
    <!-- 侧边栏头部 - Logo和标题 -->
    <div class="sidebar-header">
      <div v-if="!props.collapsed" class="logo-section">
        <img src="@/assets/images/logo.png" alt="Logo" class="logo-image" />
        <h1 class="system-title">合同审核系统</h1>
      </div>
      <div v-else class="logo-section-collapsed">
        <img src="@/assets/images/logo.png" alt="Logo" class="logo-image-small" />
      </div>
    </div>

    <!-- 菜单区域 -->
    <div class="sidebar-menu">
      <el-menu
        :default-active="activeMenu"
        :collapse="props.collapsed"
        :unique-opened="true"
        class="sidebar-menu-el"
        @select="handleMenuSelect"
      >
        <template v-for="menu in visibleMenus" :key="menu.key">
          <el-menu-item
            :index="menu.key"
            :disabled="menu.disabled"
            class="sidebar-menu-item"
          >
            <el-icon>
              <component :is="menu.icon" />
            </el-icon>
            <template #title>
              <span>{{ menu.title }}</span>
            </template>
          </el-menu-item>
        </template>
      </el-menu>
    </div>

    <!-- 侧边栏底部 -->
    <div class="sidebar-footer">
      <!-- 通知区域 -->
      <div class="notification-section">
        <el-popover
          placement="right-start"
          :width="320"
          trigger="click"
          popper-class="notification-popover"
        >
          <template #reference>
            <div class="notification-trigger">
              <el-badge
                :value="notificationStore.unreadCount"
                :hidden="notificationStore.unreadCount === 0"
                class="notification-badge"
              >
                <el-button text class="notification-btn">
                  <el-icon :size="18"><Bell /></el-icon>
                  <span v-if="!props.collapsed" class="notification-text">通知</span>
                </el-button>
              </el-badge>
            </div>
          </template>

          <!-- 通知下拉内容 -->
          <div class="notification-dropdown">
            <div class="notification-header">
              <h4>通知消息</h4>
              <el-button
                v-if="notificationStore.unreadCount > 0"
                text
                size="small"
                @click="$emit('mark-all-read')"
              >
                全部已读
              </el-button>
            </div>

            <div
              v-loading="notificationStore.loading"
              class="notification-list"
            >
              <div
                v-if="notificationStore.notifications.length === 0"
                class="notification-empty"
              >
                <el-icon :size="48" color="#c0c4cc"><Bell /></el-icon>
                <p>暂无通知</p>
              </div>

              <div
                v-for="notification in displayNotifications"
                :key="notification.id"
                class="notification-item"
                :class="{
                  'notification-item--unread': !notification.is_read,
                }"
                @click="$emit('notification-click', notification)"
              >
                <div class="notification-content">
                  <div class="notification-title">
                    {{ notification.title }}
                    <span
                      v-if="!notification.is_read"
                      class="unread-dot"
                    ></span>
                  </div>
                  <div class="notification-text">
                    {{ notification.content }}
                  </div>
                  <div class="notification-time">
                    {{ formatNotificationTime(notification.created_at) }}
                  </div>
                </div>
              </div>
            </div>

            <div class="notification-footer">
              <el-button text @click="$emit('view-all-notifications')"
                >查看全部通知</el-button
              >
            </div>
          </div>
        </el-popover>
      </div>

      <!-- 账号信息区域 -->
      <div class="user-section">
        <!-- 展开状态下的用户信息 -->
        <el-dropdown
          v-if="!props.collapsed"
          class="user-dropdown"
          @command="$emit('user-command', $event)"
        >
          <div class="user-info">
            <div class="user-avatar">
              <img
                v-if="userInfo.avatar"
                :src="userInfo.avatar"
                :alt="userInfo.name"
                class="avatar-image"
              />
              <span v-else class="avatar-initial">{{ userInfo.initial }}</span>
            </div>
            <div class="user-details">
              <span class="user-name">{{ userInfo.name }}</span>
              <span class="user-role">{{ userInfo.role }}</span>
            </div>
            <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>
                个人设置
              </el-dropdown-item>
              <el-dropdown-item command="changePassword">
                <el-icon><Lock /></el-icon>
                修改密码
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 折叠状态下的用户头像 - 也可以点击 -->
        <el-dropdown
          v-else
          class="user-dropdown-collapsed"
          placement="right-start"
          @command="$emit('user-command', $event)"
        >
          <div class="user-avatar-collapsed">
            <img
              v-if="userInfo.avatar"
              :src="userInfo.avatar"
              :alt="userInfo.name"
              class="avatar-image"
            />
            <span v-else class="avatar-initial">{{ userInfo.initial }}</span>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>
                个人设置
              </el-dropdown-item>
              <el-dropdown-item command="changePassword">
                <el-icon><Lock /></el-icon>
                修改密码
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <!-- 在线状态 -->
      <div class="status-section">
        <div class="user-status">
          <div class="status-indicator online"></div>
          <span v-if="!props.collapsed" class="status-text">在线</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, markRaw } from "vue";
import { useRoute } from "vue-router";

// 定义 props
const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false,
  },
  userInfo: {
    type: Object,
    default: () => ({
      name: "用户",
      initial: "U",
      avatar: "",
      role: "",
    }),
  },
  notificationStore: {
    type: Object,
    required: true,
  },
});
import {
  Document,
  House,
  Upload,
  DocumentCopy,
  User,
  Clock,
  Timer,
  Check,
  DataAnalysis,
  UserFilled,
  FolderOpened,
  DataBoard,
  Setting,
  Loading,
  Bell,
  ArrowDown,
  Lock,
  SwitchButton,
} from "@element-plus/icons-vue";

import { formatRelativeTime } from "@/utils/dateUtils";

import { useUserStore } from "@/stores/user";
import { usePermission } from "@/composables/usePermission";

// 定义 emits
const emit = defineEmits(["menu-select"]);

const route = useRoute();

// 认证和权限
const userStore = useUserStore();
const userRole = computed(() => userStore.userRole);
const { visibleMenus: permissionMenus } = usePermission();

// 当前激活的菜单
const activeMenu = ref("home");

// 菜单图标映射 - 使用 markRaw 避免响应式包装
const iconMap = {
  House: markRaw(House),
  Upload: markRaw(Upload),
  Document: markRaw(DocumentCopy),
  User: markRaw(User),
  Clock: markRaw(Clock),
  Timer: markRaw(Timer),
  Check: markRaw(Check),
  DataAnalysis: markRaw(DataAnalysis),
  UserFilled: markRaw(UserFilled),
  FolderOpened: markRaw(FolderOpened),
  DataBoard: markRaw(DataBoard),
  Setting: markRaw(Setting),
  Loading: markRaw(Loading),
};

// 处理菜单图标
const processMenus = (menus) => {
  return menus.map((menu) => ({
    ...menu,
    icon: iconMap[menu.icon] || markRaw(Document),
  }));
};

// 可见菜单列表 - 直接使用权限管理中的菜单配置
const visibleMenus = computed(() => {
  // 使用 usePermission 中的菜单配置，确保一致性
  const menus = processMenus(permissionMenus.value);

  return menus;
});

// 处理菜单选择
const handleMenuSelect = (index, indexPath, item, routeResult) => {
  // 只有当菜单项真正被点击时才更新激活状态
  // 这样可以避免Tab切换时菜单状态被重置
  activeMenu.value = index;

  // 查找对应的菜单项
  const menuItem = visibleMenus.value.find((menu) => menu.key === index);
  if (menuItem) {
    emit("menu-select", menuItem);
  }
};

// 从外部设置激活菜单（用于Tab切换时同步菜单状态）
const setActiveMenu = (menuKey) => {
  activeMenu.value = menuKey;
};

// 暴露方法给父组件
// 通知相关计算属性
const displayNotifications = computed(() => {
  return props.notificationStore.notifications.slice(0, 10);
});

// 格式化通知时间
const formatNotificationTime = (timeString) => {
  if (!timeString) return "";
  return formatRelativeTime(timeString);
};

defineExpose({
  setActiveMenu,
});

// 移除路由监听，避免Tab切换时菜单状态刷新
// 菜单状态现在完全由用户点击控制，不依赖路由变化
// watch(
//   () => route.path,
//   (newPath) => {
//     // 根据路由路径确定激活的菜单
//     const menuItem = visibleMenus.value.find(
//       (menu) => newPath.startsWith(menu.path) || newPath === menu.path,
//     );

//     if (menuItem) {
//       activeMenu.value = menuItem.key;
//     }
//   },
//   { immediate: true },
// );
</script>

<style scoped>
.sidebar-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  overflow: hidden; /* 防止出现滚动条 */
}

/* 侧边栏头部样式 */
.sidebar-header {
  height: 36px; /* 设置固定高度与Tab区域一致 */
  display: flex;
  align-items: center;
  justify-content: center; /* 确保内容居中 */
  padding: 0 12px; /* 减少内边距，为logo留出更多空间 */
  border-bottom: 1px solid #e4e7ed;
  background: #fff; /* 改为白色背景与Tab区域一致 */
  flex-shrink: 0;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-section-collapsed {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.logo-image {
  width: 28px; /* 调整为适合36px容器高度的尺寸 */
  height: 28px;
  object-fit: contain;
}

.logo-image-small {
  width: 24px; /* 折叠状态下稍小一些，保持视觉平衡 */
  height: 24px;
  object-fit: contain;
}

.system-title {
  font-size: 18px;
  font-weight: 700;
  color: #409eff;
  margin: 0;
  white-space: nowrap;
}

.sidebar-menu {
  flex: 1;
  overflow: hidden; /* 防止出现滚动条 */
  padding: 8px 0;
}

/* 折叠状态下的菜单容器样式优化 */
.sidebar-menu :deep(.el-menu--collapse) {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 64px;
}

.sidebar-menu-el {
  border-right: none;
}

.sidebar-menu-item {
  margin: 4px 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.sidebar-menu-item:hover {
  background-color: #ecf5ff !important;
}

.sidebar-menu-item.is-active {
  background-color: #409eff !important;
  color: #fff !important;
}

.sidebar-menu-item.is-active .el-icon {
  color: #fff !important;
}

.sidebar-menu-item .el-icon {
  font-size: 18px;
  color: #606266;
  transition: color 0.3s ease;
}

.sidebar-menu-item:hover .el-icon {
  color: #409eff;
}

.sidebar-footer {
  padding: 12px;
  border-top: 1px solid #e4e7ed;
  background: #fafafa;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 通知区域样式 */
.notification-section {
  display: flex;
  align-items: center;
  justify-content: center; /* 确保整个通知区域居中 */
}

.notification-trigger {
  width: 100%;
  display: flex;
  justify-content: center; /* 确保触发器居中 */
}

.notification-btn {
  width: 100%;
  display: flex;
  align-items: center; /* 确保垂直居中对齐 */
  justify-content: center; /* 始终居中 */
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
  color: #606266;
  min-height: 36px; /* 确保与其他元素高度一致 */
  line-height: 1; /* 重置行高，避免文字偏移 */
}

.notification-btn:hover {
  background-color: #ecf5ff;
  color: #409eff;
}

/* 折叠状态下的通知按钮 */
.sidebar-container:has(.sidebar-header .logo-section-collapsed) .notification-btn {
  padding: 8px;
  justify-content: center;
  width: auto; /* 折叠时不占满宽度 */
  min-width: 36px; /* 最小宽度确保图标居中 */
}

/* 展开状态下的通知按钮 */
.sidebar-container:has(.sidebar-header .logo-section:not(.logo-section-collapsed)) .notification-btn {
  justify-content: flex-start; /* 展开时左对齐 */
}

.notification-text {
  font-size: 14px;
  line-height: 1; /* 重置文字行高 */
  display: flex;
  align-items: center; /* 确保文字垂直居中 */
}

/* 通知图标样式 */
.notification-btn .el-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1; /* 重置图标行高 */
}

/* 确保按钮内所有元素垂直对齐 */
.notification-btn > span {
  display: flex;
  align-items: center;
  gap: 8px;
}

.notification-badge {
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-badge :deep(.el-badge__content) {
  background-color: #f56c6c;
  border: 2px solid #fff;
  font-size: 12px;
  height: 18px;
  line-height: 14px;
  min-width: 18px;
  padding: 0 4px;
  right: 5px;
  top: 5px;
}

/* 通知下拉框样式 */
:deep(.notification-popover) {
  padding: 0 !important;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.notification-dropdown {
  max-height: 400px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.notification-header {
  padding: 16px 20px 12px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
}

.notification-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.notification-list {
  flex: 1;
  overflow-y: auto;
  max-height: 300px;
}

.notification-empty {
  padding: 40px 20px;
  text-align: center;
  color: #909399;
}

.notification-empty p {
  margin: 12px 0 0;
  font-size: 14px;
}

.notification-item {
  padding: 12px 20px;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item--unread {
  background-color: #f0f9ff;
  border-left: 3px solid #409eff;
}

.notification-content {
  width: 100%;
}

.notification-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.unread-dot {
  width: 6px;
  height: 6px;
  background-color: #409eff;
  border-radius: 50%;
  flex-shrink: 0;
}

.notification-text {
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-time {
  font-size: 12px;
  color: #909399;
}

.notification-footer {
  padding: 12px 20px;
  border-top: 1px solid #f0f0f0;
  text-align: center;
  background: #fafafa;
}

/* 用户区域样式 */
.user-section {
  display: flex;
  align-items: center;
}

.user-dropdown {
  width: 100%;
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
  width: 100%;
}

.user-info:hover {
  background-color: #ecf5ff;
}

.user-avatar,
.user-avatar-collapsed {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
  overflow: hidden;
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.3s;
}

.user-avatar-collapsed:hover {
  transform: scale(1.05);
}

.user-dropdown-collapsed {
  width: 100%;
  display: flex;
  justify-content: center;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.avatar-initial {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.user-role {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.dropdown-icon {
  color: #c0c4cc;
  transition: transform 0.3s;
  flex-shrink: 0;
}

.user-dropdown:hover .dropdown-icon {
  transform: rotate(180deg);
}

/* 状态区域样式 */
.status-section {
  display: flex;
  justify-content: center;
}

.user-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #67c23a;
}

.status-indicator.online {
  background-color: #67c23a;
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.3);
}

.status-indicator.offline {
  background-color: #f56c6c;
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.3);
}

.status-text {
  font-size: 12px;
}

/* 折叠状态样式 */
:deep(.el-menu--collapse) {
  width: 64px;
}

/* 菜单项样式覆盖 */
:deep(.el-menu-item) {
  height: 48px;
  line-height: 48px;
  font-size: 14px;
  border-radius: 8px;
  margin: 4px 8px;
  padding: 0 16px;
  transition: all 0.3s ease;
}

/* 折叠状态下的菜单项特殊样式 */
:deep(.el-menu--collapse .el-menu-item) {
  width: 48px !important;
  height: 48px !important;
  margin: 2px 8px !important;
  padding: 0 !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  border-radius: 8px !important;
  text-align: center !important;
  position: relative;
  overflow: hidden;
  line-height: 48px !important;
}

/* 折叠状态下的图标样式 */
:deep(.el-menu--collapse .el-menu-item .el-icon) {
  margin: 0 !important;
  font-size: 18px !important;
  width: 18px !important;
  height: 18px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 1 !important;
}

/* 确保折叠状态下菜单项标题隐藏 */
:deep(.el-menu--collapse .el-menu-item .el-menu-item__title) {
  display: none !important;
}

/* 重置折叠状态下菜单项的所有内部元素 */
:deep(.el-menu--collapse .el-menu-item *) {
  box-sizing: border-box !important;
}

/* 确保折叠状态下菜单项内容完全居中 */
:deep(.el-menu--collapse .el-menu-item) {
  box-sizing: border-box !important;
}

:deep(.el-menu-item:hover) {
  background-color: #ecf5ff;
  color: #409eff;
}

:deep(.el-menu-item.is-active) {
  background-color: #409eff;
  color: #fff;
}

:deep(.el-menu-item.is-active:hover) {
  background-color: #337ecc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar-header {
    height: 50px;
    padding: 0 12px;
  }

  .logo-text {
    font-size: 14px;
  }

  .sidebar-footer {
    padding: 12px;
  }
}
</style>
