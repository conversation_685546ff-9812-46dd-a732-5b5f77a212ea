<template>
  <div class="tab-manager">
    <!-- 侧边栏折叠按钮 -->
    <div class="sidebar-toggle-section">
      <div class="sidebar-toggle" @click="$emit('toggle-sidebar')">
        <el-icon :size="14">
          <component :is="sidebarCollapsed ? 'Expand' : 'Fold'" />
        </el-icon>
      </div>
    </div>

    <!-- 标签页区域 -->
    <div class="tabs-section">
      <el-tabs
        v-model="activeTabName"
        type="card"
        closable
        class="tab-manager-tabs"
        @tab-click="handleTabClick"
        @tab-remove="handleTabRemove"
      >
        <el-tab-pane
          v-for="tab in tabs"
          :key="tab.name"
          :label="tab.title"
          :name="tab.name"
          :closable="tab.closable !== false"
        >
          <template #label>
            <div class="tab-label">
              <el-icon v-if="tab.icon" class="tab-icon">
                <component :is="getIconComponent(tab.icon)" />
              </el-icon>
              <span class="tab-title">{{ tab.title }}</span>
              <el-icon
                v-if="tab.modified"
                class="tab-modified"
                title="内容已修改"
              >
                <CirclePlusFilled />
              </el-icon>
            </div>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- Tab 操作按钮 -->
    <div v-if="tabs.length > 0" class="tab-actions">
      <el-dropdown trigger="click" @command="handleTabAction">
        <el-button text size="small" class="tab-action-btn">
          <el-icon><MoreFilled /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item
              command="closeOthers"
              :disabled="tabs.length <= 1"
            >
              关闭其他标签
            </el-dropdown-item>
            <el-dropdown-item command="closeAll" :disabled="tabs.length === 0">
              关闭所有标签
            </el-dropdown-item>
            <el-dropdown-item command="closeLeft" :disabled="!canCloseLeft">
              关闭左侧标签
            </el-dropdown-item>
            <el-dropdown-item command="closeRight" :disabled="!canCloseRight">
              关闭右侧标签
            </el-dropdown-item>
            <el-dropdown-item divided command="refresh">
              <el-icon><Refresh /></el-icon>
              刷新当前标签
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, markRaw } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  MoreFilled,
  CirclePlusFilled,
  Refresh,
  Document,
  House,
  Upload,
  DocumentCopy,
  User,
  Clock,
  Timer,
  Check,
  DataAnalysis,
  UserFilled,
  FolderOpened,
  DataBoard,
  Setting,
  Loading,
  Expand,
  Fold
} from "@element-plus/icons-vue";

// 定义 props
const props = defineProps({
  tabs: {
    type: Array,
    default: () => [],
  },
  activeTab: {
    type: String,
    default: "",
  },
  sidebarCollapsed: {
    type: Boolean,
    default: false,
  },
});

// 定义 emits
const emit = defineEmits(["tab-change", "tab-close", "update:activeTab"]);

const router = useRouter();

// 图标映射 - 将字符串icon名称转换为Vue组件
const iconMap = {
  House: markRaw(House),
  Upload: markRaw(Upload),
  Document: markRaw(DocumentCopy),
  User: markRaw(User),
  Clock: markRaw(Clock),
  Timer: markRaw(Timer),
  Check: markRaw(Check),
  DataAnalysis: markRaw(DataAnalysis),
  UserFilled: markRaw(UserFilled),
  FolderOpened: markRaw(FolderOpened),
  DataBoard: markRaw(DataBoard),
  Setting: markRaw(Setting),
  Loading: markRaw(Loading),
};

// 获取图标组件
const getIconComponent = (iconName) => {
  if (typeof iconName === 'string') {
    return iconMap[iconName] || markRaw(Document);
  }
  // 如果已经是组件对象，直接返回
  return iconName;
};

// 当前激活的标签页
const activeTabName = ref(props.activeTab);

// 当前激活标签的索引 - 使用缓存优化
const activeTabIndex = computed(() => {
  return props.tabs.findIndex((tab) => tab.name === activeTabName.value);
});

// 是否可以关闭左侧标签 - 优化版
const canCloseLeft = computed(() => {
  const index = activeTabIndex.value;
  return index > 0;
});

// 是否可以关闭右侧标签 - 优化版
const canCloseRight = computed(() => {
  const index = activeTabIndex.value;
  return index < props.tabs.length - 1 && index >= 0;
});

// 监听 props 变化
watch(
  () => props.activeTab,
  (newActiveTab) => {
    activeTabName.value = newActiveTab;
  },
);

watch(
  () => activeTabName.value,
  (newActiveTab) => {
    emit("tab-change", newActiveTab);
  },
);

// 处理标签页点击
const handleTabClick = (tab) => {
  const tabData = props.tabs.find((t) => t.name === tab.props.name);
  if (tabData) {
    // 阻止任何可能的默认行为
    event?.preventDefault?.();
    event?.stopPropagation?.();

    // 只通过emit通知父组件切换Tab，完全不进行路由跳转
    // 这样可以避免页面刷新，保持左侧边栏和Tab状态
    emit("update:activeTab", tabData.name);
  }
};

// 处理标签页移除
const handleTabRemove = async (tabName) => {
  const tab = props.tabs.find((t) => t.name === tabName);

  // 如果标签页有未保存的修改，提示用户
  if (tab && tab.modified) {
    try {
      await ElMessageBox.confirm(
        "当前标签页有未保存的修改，确定要关闭吗？",
        "确认关闭",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        },
      );
    } catch {
      return; // 用户取消关闭
    }
  }

  emit("tab-close", tabName);
};

// 处理标签页操作
const handleTabAction = async (command) => {
  switch (command) {
    case "closeOthers":
      await closeOtherTabs();
      break;
    case "closeAll":
      await closeAllTabs();
      break;
    case "closeLeft":
      await closeLeftTabs();
      break;
    case "closeRight":
      await closeRightTabs();
      break;
    case "refresh":
      refreshCurrentTab();
      break;
  }
};

// 关闭其他标签页
const closeOtherTabs = async () => {
  const currentTab = activeTabName.value;
  const tabsToClose = props.tabs.filter(
    (tab) => tab.name !== currentTab && tab.closable !== false,
  );

  if (await confirmCloseModifiedTabs(tabsToClose)) {
    tabsToClose.forEach((tab) => {
      emit("tab-close", tab.name);
    });
    ElMessage.success("已关闭其他标签页");
  }
};

// 关闭所有标签页
const closeAllTabs = async () => {
  const tabsToClose = props.tabs.filter((tab) => tab.closable !== false);

  if (await confirmCloseModifiedTabs(tabsToClose)) {
    tabsToClose.forEach((tab) => {
      emit("tab-close", tab.name);
    });
    ElMessage.success("已关闭所有标签页");
  }
};

// 关闭左侧标签页
const closeLeftTabs = async () => {
  const currentIndex = activeTabIndex.value;
  const tabsToClose = props.tabs
    .slice(0, currentIndex)
    .filter((tab) => tab.closable !== false);

  if (await confirmCloseModifiedTabs(tabsToClose)) {
    tabsToClose.forEach((tab) => {
      emit("tab-close", tab.name);
    });
    ElMessage.success("已关闭左侧标签页");
  }
};

// 关闭右侧标签页
const closeRightTabs = async () => {
  const currentIndex = activeTabIndex.value;
  const tabsToClose = props.tabs
    .slice(currentIndex + 1)
    .filter((tab) => tab.closable !== false);

  if (await confirmCloseModifiedTabs(tabsToClose)) {
    tabsToClose.forEach((tab) => {
      emit("tab-close", tab.name);
    });
    ElMessage.success("已关闭右侧标签页");
  }
};

// 刷新当前标签页
const refreshCurrentTab = () => {
  const currentTab = props.tabs.find((tab) => tab.name === activeTabName.value);
  if (currentTab && currentTab.path) {
    // 强制刷新路由
    router.replace({
      path: "/redirect" + currentTab.path,
    });
  }
  ElMessage.success("标签页已刷新");
};

// 确认关闭有修改的标签页
const confirmCloseModifiedTabs = async (tabs) => {
  const modifiedTabs = tabs.filter((tab) => tab.modified);

  if (modifiedTabs.length > 0) {
    try {
      await ElMessageBox.confirm(
        `有 ${modifiedTabs.length} 个标签页包含未保存的修改，确定要关闭吗？`,
        "确认关闭",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        },
      );
      return true;
    } catch {
      return false;
    }
  }

  return true;
};
</script>

<style scoped>
.tab-manager {
  display: flex;
  align-items: stretch; /* 改为stretch，让子元素填满高度 */
  height: 36px; /* 降低高度 */
  background: #fff;
  border-bottom: none; /* 移除下边框 */
}

/* 侧边栏折叠按钮区域 */
.sidebar-toggle-section {
  display: flex;
  align-items: center;
  padding: 0 8px; /* 减少内边距，使按钮区域更紧凑 */
  border-right: 1px solid #e4e7ed;
  height: 100%;
}

.sidebar-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px; /* 缩小按钮尺寸 */
  height: 20px;
  border-radius: 4px; /* 减小圆角 */
  cursor: pointer;
  transition: all 0.2s ease;
  color: #909399; /* 降低视觉重量，使用更淡的颜色 */
  opacity: 0.7; /* 增加透明度，使其更低调 */
}

.sidebar-toggle:hover {
  background-color: #f5f7fa;
  color: #409eff;
  opacity: 1; /* 悬停时恢复不透明 */
}

/* 标签页区域 */
.tabs-section {
  flex: 1;
  display: flex;
  align-items: stretch; /* 改为stretch确保完全填充高度 */
  height: 100%;
}

.tab-manager-tabs {
  flex: 1;
  margin-right: 8px;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
  max-width: 150px;
}

.tab-icon {
  font-size: 14px;
  color: #606266;
}

.tab-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 13px;
}

.tab-modified {
  font-size: 8px;
  color: #f56c6c;
}

.tab-actions {
  padding: 0 12px;
  /* 移除左边框，使Tab区域看起来更整洁 */
}

.tab-action-btn {
  padding: 4px;
  color: #606266;
}

.tab-action-btn:hover {
  color: #409eff;
  background-color: #ecf5ff;
}

/* 移除浏览器默认样式 */
* {
  outline: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}

/* 移除浏览器默认样式和多余元素 */
* {
  outline: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

/* 移除浏览器生成的多余属性 */
:deep(.el-tabs) {
  position: relative !important;
}

:deep(.el-tabs__header) {
  margin: 0 !important;
  border: none !important;
  background: transparent !important;
  padding: 0 !important;
  box-shadow: none !important;
  position: relative !important;
  overflow: visible !important;
  border-bottom: none !important; /* 确保没有下边框 */
  height: 100% !important; /* 确保填满高度 */
}

:deep(.el-tabs__nav-wrap) {
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
  background: transparent !important;
  position: static !important;
  overflow: visible !important;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none !important;
  content: none !important;
}

:deep(.el-tabs__nav) {
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
  position: static !important;
  transform: none !important;
}

:deep(.el-tabs__item) {
  height: 36px !important; /* 与容器高度保持一致 */
  line-height: 36px !important;
  padding: 0 12px !important; /* 减少内边距 */
  font-size: 13px !important; /* 稍微减小字体 */
  font-weight: 500 !important;
  border: none !important;
  border-radius: 6px 6px 0 0 !important; /* 稍微减小圆角 */
  margin: 0 3px 0 0 !important; /* 减少间距 */
  background: rgba(59, 130, 246, 0.1) !important;
  color: #495057 !important;
  transition: all 0.2s ease !important;
  position: static !important;
  overflow: visible !important;
  outline: none !important;
  box-shadow: none !important;
  cursor: pointer !important;
  border-bottom: 2px solid transparent !important;
}

:deep(.el-tabs__item::before) {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: transparent;
  transition: all 0.3s ease;
}

:deep(.el-tabs__item:hover) {
  background: rgba(59, 130, 246, 0.15) !important;
  color: #3b82f6 !important;
  border-bottom: 2px solid rgba(59, 130, 246, 0.3) !important;
}

:deep(.el-tabs__item.is-active) {
  background: #3b82f6 !important;
  color: white !important;
  font-weight: 600 !important;
  border-bottom: 2px solid #3b82f6 !important;
}

:deep(.el-tabs__item .el-icon-close) {
  font-size: 14px;
  margin-left: 8px;
  color: #adb5bd;
  transition: all 0.3s ease;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-tabs__item .el-icon-close:hover) {
  color: #ffffff;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(238, 90, 82, 0.3);
}

:deep(.el-tabs__content) {
  display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tab-label {
    max-width: 100px;
  }

  .tab-title {
    font-size: 12px;
  }

  :deep(.el-tabs__item) {
    padding: 0 8px;
    font-size: 12px;
  }

  .tab-actions {
    padding: 0 8px;
  }
}
</style>
